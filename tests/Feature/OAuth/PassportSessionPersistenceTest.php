<?php

namespace Tests\Feature\OAuth;

use App\Models\Passport\Client;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class PassportSessionPersistenceTest extends TestCase
{
    use RefreshDatabase;

    private Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test OAuth client
        $this->client = Client::create([
            'id' => 'test-session-client',
            'name' => 'Test Session Client',
            'secret' => 'test-secret',
            'redirect' => 'http://localhost/test/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);
    }

    /**
     * Test that persistOAuthParams method works correctly
     */
    public function test_persist_oauth_params_method(): void
    {
        $this->assertDatabaseIsolation();

        $email = '<EMAIL>';

        // Create a controller instance to test the trait methods
        $controller = new \App\Http\Controllers\Auth\LoginController();

        // Simulate the OAuth redirect URI with is_register=1
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $email,
        ]);

        // Set up session state
        Session::put('redirect_uri', $redirectUri);

        // Test the persistOAuthParams method
        $controller->persistOAuthParams();

        // Verify that OAuth parameters were persisted
        $this->assertTrue(session()->has('oauth_params_backup'));
        $persistedParams = session('oauth_params_backup');
        $this->assertEquals('1', $persistedParams['is_register']);
        $this->assertEquals($email, $persistedParams['email']);

        // Test the getPersistedOAuthParams method
        $retrievedParams = $controller->getPersistedOAuthParams();
        $this->assertNotNull($retrievedParams);
        $this->assertEquals('1', $retrievedParams['is_register']);
        $this->assertEquals($email, $retrievedParams['email']);

        // Test the clearPersistedOAuthParams method
        $controller->clearPersistedOAuthParams();
        $this->assertFalse(session()->has('oauth_params_backup'));
        $this->assertNull($controller->getPersistedOAuthParams());
    }

    /**
     * Test that isRegister() works correctly after session regeneration
     */
    public function test_is_register_works_after_session_regeneration(): void
    {
        $this->assertDatabaseIsolation();

        $email = '<EMAIL>';

        // Ensure user doesn't exist
        $this->assertDatabaseMissing('users', ['email' => $email]);

        // Simulate the complete OAuth flow that would set up the session correctly
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $email,
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Also simulate persisted OAuth parameters (as would happen after session regeneration)
        Session::put('oauth_params_backup', [
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $email,
        ]);

        // Request login page - should redirect to register with email
        $response = $this->get(route('login'));

        $response->assertRedirect(route('register', ['email' => $email]));

        // Verify that persisted parameters were cleared after processing
        $this->assertFalse(session()->has('oauth_params_backup'));
    }

    /**
     * Test that existing user flow works with persisted parameters
     */
    public function test_existing_user_flow_with_persisted_parameters(): void
    {
        $this->assertDatabaseIsolation();

        // Create an existing user
        $user = User::factory()->create(['email' => '<EMAIL>']);

        // Simulate the complete OAuth flow that would set up the session correctly
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $user->email,
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page - should show login form with pre-filled email
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
        $response->assertViewHas('prefill_email', $user->email);
    }

    /**
     * Test that Gmail flow works with persisted parameters
     */
    public function test_gmail_flow_with_persisted_parameters(): void
    {
        $this->assertDatabaseIsolation();

        $email = '<EMAIL>';

        // Ensure user doesn't exist
        $this->assertDatabaseMissing('users', ['email' => $email]);

        // Simulate the complete OAuth flow that would set up the session correctly
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $email,
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page - should redirect to Google SSO
        $response = $this->get(route('login'));

        $response->assertRedirect(route('passport.socialite.redirect', 'google'));

        // Verify that Google SSO email was set for registration flow
        $this->assertEquals($email, session('google_sso_email'));
    }

    /**
     * Verify database isolation for tests
     */
    protected function assertDatabaseIsolation(): void
    {
        $this->assertEquals('sqlite', config('database.default'));
        $this->assertStringContainsString(':memory:', config('database.connections.sqlite.database'));
    }
}
