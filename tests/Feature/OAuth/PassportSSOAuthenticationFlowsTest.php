<?php

namespace Tests\Feature\OAuth;

use App\Models\User;
use App\Models\Passport\Client;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class PassportSSOAuthenticationFlowsTest extends TestCase
{
    use RefreshDatabase;

    protected Client $client;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test OAuth client
        $this->client = Client::create([
            'id' => 'test-sso-client',
            'name' => 'GravityWriteDefaultRedirect',
            'secret' => 'test-secret',
            'redirect' => 'http://localhost/test/callback',
            'personal_access_client' => false,
            'password_client' => false,
            'revoked' => false,
        ]);
    }

    /**
     * Test Scenario 1: Direct Login Access - Login page loads without redirect loops
     */
    public function test_direct_login_access(): void
    {
        $this->assertDatabaseIsolation();

        $response = $this->get(route('login'));

        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
        $response->assertSee('Welcome back');
        $response->assertSee('Login to manage your account');
    }

    /**
     * Test Scenario 2: Direct Register Access - Register page loads without redirect loops
     */
    public function test_direct_register_access(): void
    {
        $this->assertDatabaseIsolation();

        $response = $this->get(route('register'));

        $response->assertStatus(200);
        $response->assertViewIs('auth.register');
        $response->assertSee('Create Account');
    }

    /**
     * Test Scenario 3: Login with Email Parameter - Currently redirects to register due to isRegister() logic
     */
    public function test_login_with_email_parameter(): void
    {
        $this->assertDatabaseIsolation();

        $response = $this->get(route('login', ['email' => '<EMAIL>']));

        // Current behavior: email parameter triggers registration flow
        $response->assertRedirect(route('register', ['email' => '<EMAIL>']));
    }

    /**
     * Test Scenario 4: Standard OAuth Flow - Redirects to login when not authenticated
     */
    public function test_standard_oauth_flow(): void
    {
        $this->assertDatabaseIsolation();

        $oauthUrl = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
        ]);

        $response = $this->get($oauthUrl);

        $response->assertRedirect(route('login'));
    }

    /**
     * Test Scenario 6: From client redirect with is_register key (No Email) - Redirects to registration page
     */
    public function test_oauth_with_is_register_no_email(): void
    {
        $this->assertDatabaseIsolation();

        // Simulate the session state that would be created by OAuth authorization flow
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page should redirect to register
        $response = $this->get(route('login'));

        $response->assertRedirect(route('register'));
    }

    /**
     * Test Scenario 7: From client redirect with is_register key (with Email) - Redirects to registration page with pre-filled email
     */
    public function test_oauth_with_is_register_with_new_email(): void
    {
        $this->assertDatabaseIsolation();

        $email = '<EMAIL>';

        // Simulate the session state that would be created by OAuth authorization flow
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $email,
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page should redirect to register with email
        $response = $this->get(route('login'));

        $response->assertRedirect(route('register', ['email' => $email]));
    }

    /**
     * Test NEW Scenario 1: From client redirect with is_register=1 and existing email - Show login form with pre-filled email
     */
    public function test_oauth_with_is_register_existing_email(): void
    {
        $this->assertDatabaseIsolation();

        // Create an existing user
        $user = User::factory()->create(['email' => '<EMAIL>']);

        // Simulate the session state that would be created by OAuth authorization flow
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $user->email,
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page should show login with pre-filled email
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
        $response->assertViewHas('prefill_email', $user->email);
    }

    /**
     * Test NEW Scenario 2: From client redirect with is_register=1 and new email - Redirect to registration with pre-filled email
     */
    public function test_oauth_with_is_register_new_email(): void
    {
        $this->assertDatabaseIsolation();

        $email = '<EMAIL>';

        // Ensure user doesn't exist
        $this->assertDatabaseMissing('users', ['email' => $email]);

        // Simulate the session state that would be created by OAuth authorization flow
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $email,
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page should redirect to register with email
        $response = $this->get(route('login'));

        $response->assertRedirect(route('register', ['email' => $email]));
    }

    /**
     * Test NEW Scenario 3: From client redirect with is_register=1 and new Gmail ID - Directly trigger Google SSO for registration
     */
    public function test_oauth_with_is_register_new_gmail(): void
    {
        $this->assertDatabaseIsolation();

        $email = '<EMAIL>';

        // Ensure user doesn't exist
        $this->assertDatabaseMissing('users', ['email' => $email]);

        // Simulate the session state that would be created by OAuth authorization flow
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
            'email' => $email,
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page should redirect to Google SSO
        $response = $this->get(route('login'));

        $response->assertRedirect(route('passport.socialite.redirect', 'google'));
    }

    /**
     * Test NEW Scenario 4: From client redirect with is_register=1 and no email parameter - Redirect to registration page (same as scenario 6)
     */
    public function test_oauth_with_is_register_no_email_parameter(): void
    {
        $this->assertDatabaseIsolation();

        // Simulate the session state that would be created by OAuth authorization flow
        $redirectUri = '/oauth/authorize?' . http_build_query([
            'client_id' => $this->client->id,
            'redirect_uri' => 'http://localhost/test/callback',
            'response_type' => 'code',
            'scope' => '',
            'is_register' => '1',
        ]);

        // Set up session state as it would be after OAuth redirect
        Session::put('redirect_uri', $redirectUri);
        Session::put('is_first_time', true);

        // Request login page should redirect to register
        $response = $this->get(route('login'));

        $response->assertRedirect(route('register'));
    }

    /**
     * Test session cleanup to prevent cross-tab interference
     */
    public function test_session_cleanup_prevents_interference(): void
    {
        $this->assertDatabaseIsolation();

        // Simulate stale session data
        Session::put('prefill_email', '<EMAIL>');
        Session::put('google_sso_email', '<EMAIL>');

        // Direct login access should clean up stale data
        $response = $this->get(route('login'));

        $response->assertStatus(200);
        $this->assertNull(Session::get('prefill_email'));
        $this->assertNull(Session::get('google_sso_email'));
    }

    /**
     * Test email existence checking functionality
     */
    public function test_email_existence_checking(): void
    {
        $this->assertDatabaseIsolation();

        // Create a user
        $user = User::factory()->create(['email' => '<EMAIL>']);

        // Create a controller instance that uses the trait to test the method
        $controller = new \App\Http\Controllers\Auth\LoginController();

        // Test existing email
        $this->assertTrue($controller->checkEmailExists($user->email));

        // Test non-existing email
        $this->assertFalse($controller->checkEmailExists('<EMAIL>'));
    }
}
